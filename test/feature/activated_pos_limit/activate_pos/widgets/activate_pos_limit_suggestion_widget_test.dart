import 'package:evoapp/feature/activated_pos_limit/activate_pos/cubit/activate_pos_suggestion_cubit.dart';
import 'package:evoapp/feature/activated_pos_limit/activate_pos/widgets/activate_pos_limit_suggestion_widget.dart';
import 'package:evoapp/feature/activated_pos_limit/activate_pos/widgets/activate_pos_suggestion_list_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider commonImageProvider;
  late EvoUtilFunction evoUtilFunction;

  const String fakeAmountUserInputted = '50';
  const int posLimit = 1000;
  const String fakeFormatCurrency = '50đ';

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    evoUtilFunction = getIt.get<EvoUtilFunction>();
    commonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerSingleton<AppState>(AppState());

    when(() => commonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(Container());

    when(() => evoUtilFunction.evoFormatCurrency(
          any(),
          currencySymbol: any(named: 'currencySymbol'),
        )).thenReturn(fakeFormatCurrency);
  });

  testWidgets('ActivatePosLimitSuggestionWidget do not render suggestions',
      (WidgetTester tester) async {
    bool isPressItemSuggestionCalled = false;
    final GlobalKey<ActivatePosLimitSuggestionWidgetState> keyActivePosLimitScreen =
        GlobalKey<ActivatePosLimitSuggestionWidgetState>();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ActivatePosLimitSuggestionWidget(
            key: keyActivePosLimitScreen,
            posLimit: posLimit,
            amountUserInputted: fakeAmountUserInputted,
            onPressItemSuggestion: (int? amount) {
              isPressItemSuggestionCalled = true;
            },
          ),
        ),
      ),
    );

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 500));

    // Emit cubit state for testing
    keyActivePosLimitScreen.currentState!.cubit
        .emit(ActivatePOSSuggestionGeneratedState(suggestions: <int>[]));

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 500));

    final Finder suggestionListFinder = find.byType(ActivatePosSuggestionListWidget);
    expect(suggestionListFinder, findsNothing);

    expect(isPressItemSuggestionCalled, false);
  });

  testWidgets('ActivatePosLimitSuggestionWidget render suggestions', (WidgetTester tester) async {
    bool isPressItemSuggestionCalled = false;
    final GlobalKey<ActivatePosLimitSuggestionWidgetState> keyActivePosLimitScreen =
        GlobalKey<ActivatePosLimitSuggestionWidgetState>();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ActivatePosLimitSuggestionWidget(
            key: keyActivePosLimitScreen,
            posLimit: posLimit,
            amountUserInputted: fakeAmountUserInputted,
            onPressItemSuggestion: (int? amount) {
              isPressItemSuggestionCalled = true;
            },
          ),
        ),
      ),
    );

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 500));

    final int fakeSuggestion1 = 2;
    final int fakeSuggestion2 = 3;

    // Emit cubit state for testing
    keyActivePosLimitScreen.currentState!.cubit.emit(
        ActivatePOSSuggestionGeneratedState(suggestions: <int>[fakeSuggestion1, fakeSuggestion2]));

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 200));

    final Finder suggestionListFinder = find.byType(ActivatePosSuggestionListWidget);
    expect(suggestionListFinder, findsOneWidget);

    final ActivatePosSuggestionListWidget suggestionListWidget =
        tester.widget<ActivatePosSuggestionListWidget>(suggestionListFinder);
    expect(suggestionListWidget.suggestions.length, 2);
    suggestionListWidget.onSuggestionTap(fakeSuggestion2);
    expect(isPressItemSuggestionCalled, true);
  });

  testWidgets('ActivatePosLimitSuggestionWidget render suggestions when UI reload',
      (WidgetTester tester) async {
    final GlobalKey<ActivatePosLimitSuggestionWidgetState> keyActivePosLimitScreen =
        GlobalKey<ActivatePosLimitSuggestionWidgetState>();

    final int fakeSuggestion1 = 2;
    final int fakeSuggestion2 = 3;
    final List<int> initialSuggestions = <int>[fakeSuggestion1, fakeSuggestion2];

    final int fakeSuggestion3 = 6;
    final int fakeSuggestion4 = 7;
    final int fakeSuggestion5 = 8;
    final List<int> updatedSuggestions = <int>[fakeSuggestion3, fakeSuggestion4, fakeSuggestion5];

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
            return ActivatePosLimitSuggestionWidget(
              key: keyActivePosLimitScreen,
              posLimit: posLimit,
              amountUserInputted: fakeAmountUserInputted,
              onPressItemSuggestion: (int? amount) async {
                // Update UI to verify didUpdateWidget() method
                setState(() {});

                // Delay to run "cubit.generateSuggestions"
                await Future<void>.delayed(const Duration(milliseconds: 200));

                // emit event cubit to test
                keyActivePosLimitScreen.currentState!.cubit
                    .emit(ActivatePOSSuggestionGeneratedState(suggestions: updatedSuggestions));
              },
            );
          }),
        ),
      ),
    );

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 200));

    // Emit cubit state for testing
    keyActivePosLimitScreen.currentState!.cubit
        .emit(ActivatePOSSuggestionGeneratedState(suggestions: initialSuggestions));

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 200));

    final Finder suggestionListFinder = find.byType(ActivatePosSuggestionListWidget);
    expect(suggestionListFinder, findsOneWidget);

    final ActivatePosSuggestionListWidget suggestionListWidget =
        tester.widget<ActivatePosSuggestionListWidget>(suggestionListFinder);
    expect(suggestionListWidget.suggestions, initialSuggestions);

    // verify method didUpdateWidget()
    final Finder activatePosLimitSuggestionFinder = find.byType(ActivatePosLimitSuggestionWidget);
    final ActivatePosLimitSuggestionWidget activatePosLimitSuggestionWidget =
        tester.widget<ActivatePosLimitSuggestionWidget>(activatePosLimitSuggestionFinder);
    activatePosLimitSuggestionWidget.onPressItemSuggestion!(fakeSuggestion2);

    await tester.pump();
    await tester.pumpAndSettle(Duration(milliseconds: 500));

    final Finder suggestionListFinder2 = find.byType(ActivatePosSuggestionListWidget);
    expect(suggestionListFinder2, findsOneWidget);
    final ActivatePosSuggestionListWidget suggestionListWidget2 =
        tester.widget<ActivatePosSuggestionListWidget>(suggestionListFinder);
    expect(suggestionListWidget2.suggestions, updatedSuggestions);
  });
}
