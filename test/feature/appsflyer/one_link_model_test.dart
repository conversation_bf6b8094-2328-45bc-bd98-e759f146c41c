import 'package:evoapp/feature/appsflyer/model/one_link_model.dart';
import 'package:evoapp/feature/deep_link/deep_link_constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OneLinkModel', () {
    test('creates a OneLinkModel with default values', () {
      // Arrange
      const String campaign = 'testCampaign';
      const String pid = 'testPid';
      const String deepLinkValue = 'testDeepLink';

      // Act
      const OneLinkModel model = OneLinkModel(
        c: campaign,
        pid: pid,
        deepLinkValue: deepLinkValue,
      );

      // Assert
      expect(model.c, campaign);
      expect(model.pid, pid);
      expect(model.deepLinkValue, deepLinkValue);
      expect(model.afXp, 'custom');
      expect(model.afDP, DeepLinkConstants.evoAppDeepLinkPrefix);
      expect(model.afForceDeeplink, true);
    });

    test('creates a OneLinkModel with specified values', () {
      // Arrange
      const String campaign = 'testCampaign';
      const String pid = 'testPid';
      const String deepLinkValue = 'testDeepLink';
      const String afXp = 'specificXp';
      const String afDP = 'https://example.com';
      const bool afForceDeeplink = false;

      // Act
      const OneLinkModel model = OneLinkModel(
        c: campaign,
        pid: pid,
        deepLinkValue: deepLinkValue,
        afXp: afXp,
        afDP: afDP,
        afForceDeeplink: afForceDeeplink,
      );

      // Assert
      expect(model.c, campaign);
      expect(model.pid, pid);
      expect(model.deepLinkValue, deepLinkValue);
      expect(model.afXp, afXp);
      expect(model.afDP, afDP);
      expect(model.afForceDeeplink, afForceDeeplink);
    });
  });
}
