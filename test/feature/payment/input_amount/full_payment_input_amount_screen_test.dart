import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/check_display_pay_with_emi/check_display_pay_with_emi_cubit.dart';
import 'package:evoapp/feature/payment/input_amount/amount_input_cubit.dart';
import 'package:evoapp/feature/payment/input_amount/bloc/payment_input_amount_cubit.dart';
import 'package:evoapp/feature/payment/input_amount/full_payment_input_amount/full_payment_input_amount_screen.dart';
import 'package:evoapp/feature/payment/utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';
import '../base_page_payment/cubit/update_order_cubit_test.dart';

class MockPaymentInputAmountCubit extends Mock implements PaymentInputAmountCubit {}

class MockActivePosLimitHandler extends Mock implements ActivePosLimitHandler {}

class MockAmountInputCubit extends Mock implements AmountInputCubit {}

class MockCheckDisplayPayWithEMICubit extends Mock implements CheckDisplayPayWithEMICubit {}

class MockTextEditingController extends Mock implements TextEditingController {}

class MockPaymentWithEMIUtils extends Mock implements PaymentWithEMIUtils {}

class TestFullPaymentInputAmountScreenState extends FullPaymentInputAmountState {
  final FullPaymentInputAmountScreen _widget = FullPaymentInputAmountScreen(
    shouldShowEMIUnqualifiedBottomSheet: true,
  );

  @override
  FullPaymentInputAmountScreen get widget => _widget;

  final PaymentInputAmountCubit _paymentInputAmountCubit = MockPaymentInputAmountCubit();

  @override
  PaymentInputAmountCubit get paymentInputAmountCubit => _paymentInputAmountCubit;

  final ActivePosLimitHandler _activePosLimitHandler = MockActivePosLimitHandler();

  @override
  ActivePosLimitHandler get activePosLimitHandler => _activePosLimitHandler;

  final AmountInputCubit _amountInputCubit = MockAmountInputCubit();

  @override
  AmountInputCubit get amountInputCubit => _amountInputCubit;

  final CheckDisplayPayWithEMICubit _checkDisplayPayWithEMICubit =
      MockCheckDisplayPayWithEMICubit();

  @override
  CheckDisplayPayWithEMICubit get checkDisplayPayWithEMICubit => _checkDisplayPayWithEMICubit;

  final TextEditingController _amountInputController = MockTextEditingController();

  @override
  TextEditingController get amountInputController => _amountInputController;

  final PaymentWithEMIUtils _paymentWithEMIUtils = MockPaymentWithEMIUtils();

  @override
  PaymentWithEMIUtils get paymentWithEMIUtils => _paymentWithEMIUtils;

  @override
  bool get isProductCodeOfVNPay => false;
}

void main() {
  late TestFullPaymentInputAmountScreenState screenState;

  setUpAll(() {
    registerFallbackValue(PaymentService.outrightPurchase);
    registerFallbackValue(Screen.paymentInputAmount);
    registerFallbackValue(OrderActivatePosLimitEntity(paymentService: null));
  });

  setUp(() {
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());

    // Setup utils for page state
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpOneLinkDeepLinkRegExForTest();

    // Override getIt registrations
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerFactory<CheckOutRepo>(() => MockCheckOutRepo());

    when(() => commonUtilFunction.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async => Future<void>.value());

    setUpMockSnackBarForTest();
    // Mock EvoUiUtils
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(100);

    when(() => EvoUiUtils().hideKeyboard()).thenAnswer((_) async {});

    setupMockDialogHelper();

    // Create screen state for testing
    screenState = TestFullPaymentInputAmountScreenState();
  });

  tearDown(() {
    getIt.reset();
  });

  void setupMockDataForScreenBlocAndUtils(TestFullPaymentInputAmountScreenState state) {
    // Mock PaymentInputAmountCubit methods
    final MockPaymentInputAmountCubit mockPaymentInputAmountCubit =
        state.paymentInputAmountCubit as MockPaymentInputAmountCubit;
    when(() => mockPaymentInputAmountCubit.validateSubmittedAmount(
          amountStr: any(named: 'amountStr'),
          paymentService: any(named: 'paymentService'),
          productCode: any(named: 'productCode'),
        )).thenAnswer((_) {});
    when(() => mockPaymentInputAmountCubit.updateNoteText(any())).thenAnswer((_) {});
    when(() => mockPaymentInputAmountCubit.createOrder(
          storeId: any(named: 'storeId'),
          amount: any(named: 'amount'),
          note: any(named: 'note'),
          productCode: any(named: 'productCode'),
          paymentService: any(named: 'paymentService'),
          vnPayQrInfoEntity: any(named: 'vnPayQrInfoEntity'),
        )).thenAnswer((_) async {});

    // Mock ActivePosLimitHandler methods
    final MockActivePosLimitHandler mockActivePosLimitHandler =
        state.activePosLimitHandler as MockActivePosLimitHandler;
    when(() => mockActivePosLimitHandler.handleActivateCardOrPosLimitIfNeed(
          entryPoint: any(named: 'entryPoint'),
          onActivatedPOSLimitFlowSucceed: any(named: 'onActivatedPOSLimitFlowSucceed'),
          orderActivatePosLimitEntity: any(named: 'orderActivatePosLimitEntity'),
        )).thenAnswer((_) async => true);

    // Mock AmountInputCubit methods
    final MockAmountInputCubit mockAmountInputCubit =
        state.amountInputCubit as MockAmountInputCubit;
    when(() => mockAmountInputCubit.validateChangedAmount(any())).thenAnswer((_) {});

    // Mock CheckDisplayPayWithEMICubit methods
    final MockCheckDisplayPayWithEMICubit mockCheckDisplayPayWithEMICubit =
        state.checkDisplayPayWithEMICubit as MockCheckDisplayPayWithEMICubit;
    when(() => mockCheckDisplayPayWithEMICubit.checkDisplayPayWithEMI(
          amountStr: any(named: 'amountStr'),
          productCode: any(named: 'productCode'),
          merchantId: any(named: 'merchantId'),
        )).thenAnswer((_) {});
    when(() => mockCheckDisplayPayWithEMICubit.hidePayWithEmi()).thenAnswer((_) {});

    // Mock TextEditingController methods
    final MockTextEditingController mockAmountInputController =
        state.amountInputController as MockTextEditingController;
    when(() => mockAmountInputController.text).thenReturn('');
    when(() => mockAmountInputController.addListener(any())).thenAnswer((_) {});
    when(() => mockAmountInputController.removeListener(any())).thenAnswer((_) {});
    when(() => mockAmountInputController.dispose()).thenAnswer((_) {});

    // Mock PaymentWithEMIUtils methods
    final MockPaymentWithEMIUtils mockPaymentWithEMIUtils =
        state.paymentWithEMIUtils as MockPaymentWithEMIUtils;
    when(() => mockPaymentWithEMIUtils.showPosLimitBottomSheetIfNeed())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockPaymentWithEMIUtils.isEmiFeatureEnabled()).thenReturn(true);
    when(() => mockPaymentWithEMIUtils.getAmountFromStrWithCurrency(any())).thenReturn(10000);
    when(() => mockPaymentWithEMIUtils.canPayWithEMI(any(), any(), any())).thenReturn(true);
  }

  group('Test FullPaymentInputAmountScreen method', () {
    test('pushName', () {
      FullPaymentInputAmountScreen.pushNamed();
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.paymentInputAmount.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    test('createState', () {
      final State<FullPaymentInputAmountScreen> state = FullPaymentInputAmountScreen(
        shouldShowEMIUnqualifiedBottomSheet: false,
      ).createState();
      expect(state, isA<State<FullPaymentInputAmountScreen>>());
    });

    test('routeSettings', () {
      final RouteSettings routeSettings = FullPaymentInputAmountScreen(
        shouldShowEMIUnqualifiedBottomSheet: false,
      ).routeSettings;
      expect(routeSettings.name, Screen.paymentInputAmount.routeName);
    });
  });

  group('BasePaymentInputAmountScreenState test', () {
    testWidgets('test initState method', (WidgetTester tester) async {
      // Setup all mocks
      setupMockDataForScreenBlocAndUtils(screenState);
      screenState.initState();
      tester.binding.scheduleForcedFrame();
      await tester.pumpAndSettle(Duration(milliseconds: 100));
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            onClickClose: any(named: 'onClickClose'),
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            isDismissible: any(named: 'isDismissible'),
            content: any(named: 'content'),
            textNegative: any(named: 'textNegative'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);
    });
    test('onPaymentWithEMI', () {
      setupMockDataForScreenBlocAndUtils(screenState);
      screenState.onPaymentWithEMI();
      verify(() => screenState.paymentInputAmountCubit.validateSubmittedAmount(
            amountStr: any(named: 'amountStr'),
            paymentService: any(named: 'paymentService'),
            productCode: any(named: 'productCode'),
          )).called(1);
    });

    test('onAmountInputChanged', () {
      setupMockDataForScreenBlocAndUtils(screenState);
      screenState.onAmountInputChanged('');
      verify(() => screenState.checkDisplayPayWithEMICubit.checkDisplayPayWithEMI(
            amountStr: any(named: 'amountStr'),
            productCode: any(named: 'productCode'),
            merchantId: any(named: 'merchantId'),
          )).called(1);
    });
    test('buildBodyContent', () {
      setupMockDataForScreenBlocAndUtils(screenState);
      final Widget content = screenState.buildBodyContent();
      expect(content, isA<Column>());
    });

    test('buildEMIButton', () {
      setupMockDataForScreenBlocAndUtils(screenState);
      final BlocBuilder<CheckDisplayPayWithEMICubit, CheckDisplayWithEMIState> emitButtonBuilder =
          screenState.buildEMIButton()
              as BlocBuilder<CheckDisplayPayWithEMICubit, CheckDisplayWithEMIState>;
      final Widget widgetPayWithEMIIsDisplayed =
          emitButtonBuilder.builder(mockNavigatorContext, PayWithEMIIsDisplayed());
      expect(widgetPayWithEMIIsDisplayed, isA<Padding>());
      final Widget widgetPayWithEMIIsHidden =
          emitButtonBuilder.builder(mockNavigatorContext, PayWithEMIIsHidden());
      expect(widgetPayWithEMIIsHidden, isA<SizedBox>());
    });
  });
}
