import 'package:evoapp/data/response/user_deletion_verification_entity.dart';
import 'package:evoapp/feature/delete_account/survey/deletion_reason_grid_view.dart';
import 'package:evoapp/feature/delete_account/survey/model/delete_account_reason_model.dart';
import 'package:evoapp/feature/delete_account/survey/reason_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  test('DeletionReasonGridView const values are correct', () {
    const double expectedChildAspectRatioItem = 163.5 / 84;
    // Verify that the childAspectRatioItem value is correct
    expect(DeletionReasonGridView.childAspectRatioItem, expectedChildAspectRatioItem);

    // Verify that the defaultGridDelegate values are correct
    expect(DeletionReasonGridView.defaultGridDelegate.crossAxisCount, 2);
    expect(DeletionReasonGridView.defaultGridDelegate.crossAxisSpacing, 8.0);
    expect(DeletionReasonGridView.defaultGridDelegate.mainAxisSpacing, 8.0);
    expect(
        DeletionReasonGridView.defaultGridDelegate.childAspectRatio, expectedChildAspectRatioItem);
  });

  testWidgets('DeletionReasonGridView displays reasons correctly', (WidgetTester tester) async {
    // Create a list of reasons for testing
    final List<DeleteAccountReasonModel> testReasons = <DeleteAccountReasonModel>[
      DeleteAccountReasonModel(reason: ReasonEntity(id: 1, detail: 'Reason 1'), isSelected: false),
      DeleteAccountReasonModel(reason: ReasonEntity(id: 2, detail: 'Reason 2'), isSelected: false),
    ];

    // Create a function to pass to the DeletionReasonGridView
    int onReasonTapMethodCount = 0;
    onReasonTap(ReasonEntity entity) {
      onReasonTapMethodCount++;
    }

    // Build the DeletionReasonGridView widget
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: DeletionReasonGridView(
          deletionReasons: testReasons,
          onReasonTap: onReasonTap,
        ),
      ),
    ));

    // Verify that the correct number of reasons is displayed
    expect(find.byType(ReasonItemWidget), findsNWidgets(testReasons.length));

    // Verify that the reasons have the correct text
    expect(find.text('Reason 1'), findsOneWidget);
    expect(find.text('Reason 2'), findsOneWidget);

    // Verify Tap on the grid items
    await tester.tap(find.text('Reason 1'));
    await tester.tap(find.text('Reason 2'));
    await tester.pump();
    expect(onReasonTapMethodCount, 2);
  });
}
