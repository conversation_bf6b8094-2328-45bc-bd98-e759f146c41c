import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/feature/payment/widget/store_image_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/transaction_history_item_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_info_item.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_payment_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'transaction_history_util.dart';

void main() {
  late CommonImageProvider imageProvider;
  late EvoUtilFunction evoUtilFunction;
  late CommonUtilFunction commonUtilFunction;
  final StoreInfoEntity storeInfoEntity = StoreInfoEntity(
    thumbnail: 'thumbnail',
    address: 'fake_address',
  );

  final PaymentMethodEntity paymentMethodEntity = PaymentMethodEntity(
    sourceName: 'fake_source_name',
  );

  const double imageSizeExpect = 50;
  const double itemPaddingExpect = 10;
  const int userChargeAmountExpect = 1000;
  const String createAtValue = '2023-03-31T16:52:02+07:00';
  const String paymentServiceEmi = 'emi';
  const String transactionStatusModelProcessing = 'processing';

  setUpAll(() {
    TransactionHistoryUtil.instance.initSetUpAll();
    imageProvider = getIt.get<CommonImageProvider>();
    evoUtilFunction = getIt.get<EvoUtilFunction>();
    commonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => Container(),
    );

    when(() => imageProvider.network(
          any(),
          height: any(named: 'height'),
          width: any(named: 'width'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          placeholder: any(named: 'placeholder'),
          errorWidget: any(named: 'errorWidget'),
          onLoadError: any(named: 'onLoadError'),
        )).thenAnswer((_) => Container());
    when(
      () => evoUtilFunction.evoFormatCurrency(
        any(),
        currencySymbol: any(named: 'currencySymbol'),
      ),
    ).thenAnswer((_) => 'evoFormatCurrency');

    when(() => commonUtilFunction.toDateTime(any())).thenReturn(DateTime(2023, 3, 31, 16, 52));
  });

  Widget buildMaterialApp(PaymentResultTransactionEntity transaction, VoidCallback onTap) {
    return MaterialApp(
      home: Scaffold(
        body: TransactionHistoryItemWidget(
          transaction: transaction,
          imageSize: 50,
          imageSizeRightMargin: 10,
          itemPadding: 10,
          onTap: onTap,
        ),
      ),
    );
  }

  testWidgets(
      'test UI TransactionHistoryItemWidget with transaction has status is processing'
      'and payment service is emi', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: transactionStatusModelProcessing,
      storeInfo: storeInfoEntity,
      paymentMethod: paymentMethodEntity,
      userChargeAmount: userChargeAmountExpect,
      paymentService: paymentServiceEmi,
      createdAt: createAtValue,
    );
    bool isTap = false;

    await widgetTester.pumpWidget(buildMaterialApp(transaction, () {
      isTap = true;
    }));

    final Finder finderTransactionHistoryItemWidget = find.byType(TransactionHistoryItemWidget);

    //verify onTap
    await widgetTester.tap(finderTransactionHistoryItemWidget);
    await widgetTester.pump();
    expect(isTap, true);

    //verify Padding
    final Finder finderPadding = find.byType(Padding);
    expect(finderPadding, findsAtLeastNWidgets(1));

    final Padding padding = widgetTester.widget(finderPadding.first);
    expect(padding.padding, const EdgeInsets.all(itemPaddingExpect));

    //verify Row
    final Finder finderRow = find.byType(Row);
    expect(finderRow, findsAtLeastNWidgets(1));

    final Row row = widgetTester.widget(finderRow.first);
    expect(row.crossAxisAlignment, CrossAxisAlignment.start);

    // verify store image
    final Finder finderStoreImageWidget = find.byType(StoreImageWidget);
    expect(finderStoreImageWidget, findsOneWidget);

    final StoreImageWidget storeImageWidget = widgetTester.widget(finderStoreImageWidget);
    expect(storeImageWidget.size, imageSizeExpect);
    expect(storeImageWidget.imageUrl, storeInfoEntity.thumbnail);
    verify(() => imageProvider.asset(
          EvoImages.icPreThumbnailImage,
          height: imageSizeExpect,
          width: imageSizeExpect,
          cornerRadius: 8,
        )).called(1);

    //verify payment item
    final Finder finderRightContent = find.byType(TransactionHistoryPaymentItem);
    expect(finderRightContent, findsOneWidget);

    //verify info item
    final Finder finderMiddleContent = find.byType(TransactionHistoryInfoItem);
    expect(finderMiddleContent, findsOneWidget);
  });
}
