import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/ekyc_repo.dart';
import 'package:evoapp/data/response/ekyc_face_matching_result_entity.dart';
import 'package:evoapp/data/response/ekyc_info_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/ekyc/ekyc_bridge/ekyc_bridge.dart';
import 'package:evoapp/feature/ekyc/face_otp/face_matching/face_otp_matching_process_cubit.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/test_util.dart';

class MockEKYCRepo extends Mock implements EKYCRepo {}

class MockEKYCBridge extends Mock implements EkycBridge {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockEKYCRepo mockEKYCRepo;
  late MockEKYCBridge mockEKYCBridge;
  late MockAuthenticationRepo mockAuthenticationRepo;
  late EvoUtilFunction mockEvoUtilFunction;

  EKYCSessionEntity? mockSessionEntity;
  const String selfieImageId = 'test_selfie_image_id';

  Future<EKYCSessionEntity> stubSessionToken() async {
    final Map<String, dynamic> responseData =
        await TestUtil.getResponseMock('ekyc_face_otp_matching_link_card_value.json');
    final BaseResponse baseResponse =
        BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);

    final EKYCSessionEntity mockEntity = EKYCSessionEntity.fromBaseResponse(baseResponse);
    when(() => mockEKYCBridge.getSession()).thenReturn(mockEntity);

    return mockEntity;
  }

  Future<void> subFaceOtpMatching({required bool isSuccess}) async {
    final String fileName = isSuccess
        ? 'ekyc_face_otp_matching_link_card_value.json'
        : 'ekyc_face_otp_unknown_error_value.json';

    final Map<String, dynamic> faceOTPResponseData = await TestUtil.getResponseMock(fileName);

    when(() => mockEKYCRepo.faceOtp(
          selfieImageId: any(named: 'selfieImageId'),
          sessionToken: any(named: 'sessionToken'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => EkycFaceMatchingResultEntity.fromBaseResponse(BaseResponse(
          statusCode: isSuccess ? CommonHttpClient.SUCCESS : CommonHttpClient.UNKNOWN_ERRORS,
          response: faceOTPResponseData,
        )));
  }

  setUpAll(() {
    registerFallbackValue(TypeLogin.faceOTP);

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    when(() => mockEvoUtilFunction.updateProcessUserStatus(any()))
        .thenAnswer((_) => Future<void>.value());

    when(() => mockEvoUtilFunction.getFacialVerificationVersion()).thenReturn(
      FacialVerificationVersion.version_2,
    );
  });

  setUp(() async {
    mockEKYCRepo = MockEKYCRepo();
    mockEKYCBridge = MockEKYCBridge();
    mockAuthenticationRepo = MockAuthenticationRepo();

    mockSessionEntity = await stubSessionToken();
  });

  tearDown(() {
    mockSessionEntity = null;
  });

  group('test constructor', () {
    test('should init FaceOtpMatchingInit state', () {
      final FaceOtpMatchingProcessCubit cubit = FaceOtpMatchingProcessCubit(
        ekycRepo: mockEKYCRepo,
        ekycBridge: mockEKYCBridge,
        authenticationRepo: mockAuthenticationRepo,
      );
      expect(cubit.state, isA<FaceOtpMatchingProcessInit>());
    });
  });

  group('test faceMatchingForLinkCard()', () {
    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingCompleted when faceOtp is successful',
      setUp: () async {
        await subFaceOtpMatching(isSuccess: true);
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForLinkCard(selfieImageId: 'test_selfie_image_id'),
      expect: () => <dynamic>[isA<FaceOtpMatchingSuccess>()],
      verify: (FaceOtpMatchingProcessCubit cubit) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockEKYCRepo.faceOtp(
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);
      },
    );

    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingError when faceOtp is not successful',
      setUp: () async {
        await subFaceOtpMatching(isSuccess: false);
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForLinkCard(selfieImageId: selfieImageId),
      expect: () => <dynamic>[isA<FaceOtpMatchingError>()],
      verify: (_) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockEKYCRepo.faceOtp(
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);
      },
    );

    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingLimitExceed when faceOtp result is limit exceeded',
      setUp: () async {
        final Map<String, dynamic> faceOTPResponseData =
            await TestUtil.getResponseMock('ekyc_face_otp_limited_exceed_value.json');

        when(() => mockEKYCRepo.faceOtp(
              selfieImageId: any(named: 'selfieImageId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => EkycFaceMatchingResultEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
              response: faceOTPResponseData,
            )));
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForLinkCard(selfieImageId: 'test_selfie_image_id'),
      expect: () => <dynamic>[isA<FaceOtpMatchingLimitExceed>()],
      verify: (_) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockEKYCRepo.faceOtp(
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);
      },
    );

    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingLimitExceed when faceOtp result is unqualified & remaining_attempt == 0',
      setUp: () async {
        final Map<String, dynamic> faceOTPResponseData = await TestUtil.getResponseMock(
            'ekyc_face_otp_unknown_error_value_and_limit_exceeded.json');

        when(() => mockEKYCRepo.faceOtp(
              selfieImageId: any(named: 'selfieImageId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => EkycFaceMatchingResultEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceOTPResponseData,
            )));
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForLinkCard(selfieImageId: 'test_selfie_image_id'),
      expect: () => <dynamic>[isA<FaceOtpMatchingLimitExceed>()],
      verify: (_) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockEKYCRepo.faceOtp(
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);
      },
    );
  });

  group('test faceMatchingForSignIn()', () {
    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingCompleted when faceOtp is successful',
      setUp: () async {
        final Map<String, dynamic> faceOTPResponseData =
            await TestUtil.getResponseMock('ekyc_face_otp_matching_sign_in_value.json');

        when(() => mockAuthenticationRepo.login(
              any(),
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: any(named: 'selfieImageId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceOTPResponseData,
            )));
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForSignIn(selfieImageId: 'test_selfie_image_id'),
      expect: () => <dynamic>[isA<FaceOtpMatchingSuccess>()],
      verify: (FaceOtpMatchingProcessCubit cubit) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockAuthenticationRepo.login(
              TypeLogin.faceOTP,
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);

        verify(() => mockEvoUtilFunction.updateProcessUserStatus(any())).called(1);
      },
    );

    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingError when faceOtp is not successful',
      setUp: () async {
        when(() => mockAuthenticationRepo.login(
              any(),
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: any(named: 'selfieImageId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.UNKNOWN_ERRORS,
              response: null,
            )));
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForSignIn(selfieImageId: selfieImageId),
      expect: () => <dynamic>[isA<FaceOtpMatchingError>()],
      verify: (_) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockAuthenticationRepo.login(
              TypeLogin.faceOTP,
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);

        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );

    blocTest<FaceOtpMatchingProcessCubit, FaceOtpMatchingProcessState>(
      'should emit FaceOtpMatchingLimitExceed when faceOtp result is limit exceeded',
      setUp: () async {
        final Map<String, dynamic> faceOTPResponseData =
            await TestUtil.getResponseMock('ekyc_face_otp_limited_exceed_value.json');

        when(() => mockAuthenticationRepo.login(
              TypeLogin.faceOTP,
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: any(named: 'selfieImageId'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => SignInOtpEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
              response: faceOTPResponseData,
            )));
      },
      build: () {
        return FaceOtpMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          ekycBridge: mockEKYCBridge,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceOtpMatchingProcessCubit cubit) =>
          cubit.faceMatchingForSignIn(selfieImageId: 'test_selfie_image_id'),
      expect: () => <dynamic>[isA<FaceOtpMatchingLimitExceed>()],
      verify: (_) {
        verify(() => mockEKYCBridge.getSession()).called(1);

        // assert capture data
        final List<dynamic> capturedData = verify(() => mockAuthenticationRepo.login(
              TypeLogin.faceOTP,
              facialVerificationVersion: FacialVerificationVersion.version_2,
              selfieImageId: captureAny(named: 'selfieImageId'),
              sessionToken: captureAny(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(capturedData[0], selfieImageId);
        expect(capturedData[1], mockSessionEntity?.sessionToken);

        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );
  });

  group('verify isLimitedExceeded()', () {
    late FaceOtpMatchingProcessCubit cubit;

    setUp(() {
      cubit = FaceOtpMatchingProcessCubit(
        ekycRepo: mockEKYCRepo,
        ekycBridge: mockEKYCBridge,
        authenticationRepo: mockAuthenticationRepo,
      );
    });

    test('should return true when remainAttempt is 0', () {
      final bool result = cubit.isLimitedExceeded(
        remainAttempt: 0,
        statusCode: null,
        verdict: null,
      );
      expect(result, true);
    });

    test(
        'should return true when statusCode is LIMIT_EXCEEDED and verdict is verdictFaceOtpExceeded',
        () {
      final bool result = cubit.isLimitedExceeded(
        remainAttempt: 1,
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: EkycFaceMatchingResultEntity.verdictFaceOtpExceeded,
      );
      expect(result, true);
    });

    test('should return false when remainAttempt is not 0 and statusCode is not LIMIT_EXCEEDED',
        () {
      final bool result = cubit.isLimitedExceeded(
        remainAttempt: 1,
        statusCode: 200,
        verdict: null,
      );
      expect(result, false);
    });

    test(
        'should return false when remainAttempt is not 0 and verdict is not verdictFaceOtpExceeded',
        () {
      final bool result = cubit.isLimitedExceeded(
        remainAttempt: 1,
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: 'some_other_verdict',
      );
      expect(result, false);
    });

    test('should return false when remainAttempt is 1 and statusCode is null and verdict is null',
        () {
      final bool result = cubit.isLimitedExceeded(
        remainAttempt: 1,
        statusCode: null,
        verdict: null,
      );
      expect(result, false);
    });
  });
}
