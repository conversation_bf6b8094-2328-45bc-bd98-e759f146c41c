import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_capture_introduction/cubit/dop_native_id_capture_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_capture_introduction/cubit/dop_native_id_capture_state.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';

class MockEkycUiOnlyBridge extends Mock implements EkycUiOnlyBridge {}

void main() {
  late DOPNativeIDCaptureCubit cubit;
  late EkycUiOnlyBridge mockEkycUiOnlyBridge;

  const String mockCardType = 'CMND';
  const String mockQRCodeBase64Image = 'mockQRCodeBase64Image';
  const String mockFrontSideBase64Image = 'mockFrontSideBase64Image';
  const String mockBackSideBase64Image = 'mockBackSideBase64Image';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockEkycUiOnlyBridge = MockEkycUiOnlyBridge();

    cubit = DOPNativeIDCaptureCubit(
      ekycUiOnlyBridge: mockEkycUiOnlyBridge,
    );
  });

  tearDown(() {
    reset(mockEkycUiOnlyBridge);
  });

  test('initial state is DOPNativeIdCaptureInitialState', () {
    expect(cubit.state, isA<DOPNativeIdCaptureInitialState>());
  });

  group('startQRCodeCapture', () {
    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureQRCodeSuccessState] when QR code capture is successful',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startQRCodeCapturing(
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getQrFrontSideBase64Image())
            .thenReturn(mockQRCodeBase64Image);
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startQRCodeCapture(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureQRCodeSuccessState>().having(
          (DOPNativeIdCaptureQRCodeSuccessState state) => state.qrCodeBase64Image,
          'qrCodeBase64Image',
          equals(mockQRCodeBase64Image),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startQRCodeCapturing(skipConfirmScreen: true)).called(1);
        verify(() => mockEkycUiOnlyBridge.getQrFrontSideBase64Image()).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureQRCodeTimeoutState] when QR code capture timeout',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startQRCodeCapturing(
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.qrTimeout));
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startQRCodeCapture(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureQRCodeTimeoutState>().having(
          (DOPNativeIdCaptureQRCodeTimeoutState state) => state.cardType,
          'cardType',
          equals(mockCardType),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startQRCodeCapturing(skipConfirmScreen: true)).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureQRCodeBypassState] when QR code capture fails but not due to timeout or user cancellation',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startQRCodeCapturing(
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.unknown));
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startQRCodeCapture(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureQRCodeBypassState>().having(
          (DOPNativeIdCaptureQRCodeBypassState state) => state.cardType,
          'cardType',
          equals(mockCardType),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startQRCodeCapturing(skipConfirmScreen: true)).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits nothing when QR code capture is cancelled by user',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startQRCodeCapturing(
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.userCancelled));
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startQRCodeCapture(),
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startQRCodeCapturing(skipConfirmScreen: true)).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureQRCodeBypassState] when SDK is successful but QR code image is null',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startQRCodeCapturing(
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getQrFrontSideBase64Image()).thenReturn(null);
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startQRCodeCapture(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureQRCodeBypassState>().having(
          (DOPNativeIdCaptureQRCodeBypassState state) => state.cardType,
          'cardType',
          equals(mockCardType),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startQRCodeCapturing(skipConfirmScreen: true)).called(1);
        verify(() => mockEkycUiOnlyBridge.getQrFrontSideBase64Image()).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );
  });

  group('startIdCardCapture - front side', () {
    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureFrontSideSuccessState] when front ID card capture is successful',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getIdFrontSideBase64Image())
            .thenReturn(mockFrontSideBase64Image);
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: false),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureFrontSideSuccessState>().having(
          (DOPNativeIdCaptureFrontSideSuccessState state) => state.frontSideBase64Image,
          'frontSideBase64Image',
          equals(mockFrontSideBase64Image),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).called(1);
        verify(() => mockEkycUiOnlyBridge.getIdFrontSideBase64Image()).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureFrontSideErrorState] when front ID card capture fails',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.unknown));
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: false),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureFrontSideErrorState>().having(
          (DOPNativeIdCaptureFrontSideErrorState state) => state.failReason,
          'failReason',
          equals(TVSDKFailReason.unknown),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureFrontSideErrorState] when front ID card capture is successful but image is null',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getIdFrontSideBase64Image()).thenReturn(null);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: false),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureFrontSideErrorState>(),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: false,
              skipConfirmScreen: true,
            )).called(1);
        verify(() => mockEkycUiOnlyBridge.getIdFrontSideBase64Image()).called(1);
      },
    );
  });

  group('startIdCardCapture - back side', () {
    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureBackSideSuccessState] when back ID card capture is successful',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getIdBackSideBase64Image())
            .thenReturn(mockBackSideBase64Image);
        when(() => mockEkycUiOnlyBridge.getCardTypeId()).thenReturn(mockCardType);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureBackSideSuccessState>().having(
          (DOPNativeIdCaptureBackSideSuccessState state) => state.backSideBase64Image,
          'backSideBase64Image',
          equals(mockBackSideBase64Image),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).called(1);
        verify(() => mockEkycUiOnlyBridge.getIdBackSideBase64Image()).called(1);
        verify(() => mockEkycUiOnlyBridge.getCardTypeId()).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureBackSideErrorState] when back ID card capture fails',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.failed(reason: TVSDKFailReason.unknown));
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureBackSideErrorState>().having(
          (DOPNativeIdCaptureBackSideErrorState state) => state.failReason,
          'failReason',
          equals(TVSDKFailReason.unknown),
        ),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).called(1);
      },
    );

    blocTest<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
      'emits [DOPNativeIdCaptureBackSideErrorState] when back ID card capture is successful but image is null',
      build: () => cubit,
      setUp: () {
        when(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).thenAnswer((_) async => TVSDKResult.succeed());
        when(() => mockEkycUiOnlyBridge.getIdBackSideBase64Image()).thenReturn(null);
      },
      act: (DOPNativeIDCaptureCubit cubit) => cubit.startIdCardCapture(isBackSide: true),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIdCaptureBackSideErrorState>(),
      ],
      verify: (_) {
        verify(() => mockEkycUiOnlyBridge.startIdCapturing(
              isReadBothSide: false,
              isBackSide: true,
              skipConfirmScreen: true,
            )).called(1);
        verify(() => mockEkycUiOnlyBridge.getIdBackSideBase64Image()).called(1);
      },
    );
  });
}
