import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/payment_result_entity.dart';
import '../../../data/response/payment_result_transaction_entity.dart';
import '../../payment/mock_file/mock_checkout_file_name.dart';

part 'transaction_detail_state.dart';

class TransactionDetailCubit extends CommonCubit<TransactionDetailState> {
  final CheckOutRepo _checkOutRepo;

  TransactionDetailCubit(this._checkOutRepo) : super(TransactionDetailInitial());

  Future<void> getTransactionDetail({
    required String? transactionId,
    bool isRefresh = false,
  }) async {
    if (!isRefresh) {
      emit(TransactionDetailLoading());
    }

    final PaymentResultEntity entity = await _checkOutRepo.getTransactionDetail(
        transactionId: transactionId,
        mockConfig: MockConfig(
          enable: false,
          fileName: getTransactionDetailMockFileName(),
        ));

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(TransactionDetailSuccess(transaction: entity.transaction));
    } else {
      emit(
        TransactionDetailError(
          error: ErrorUIModel.fromEntity(entity),
          isRefresh: isRefresh,
        ),
      );
    }
  }
}
