import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../../flavors/flavors_type.dart';

/// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-3010
class OneLinkURLConstants {
  /// One link on staging environment
  static const String oneLinkStagingUrl = 'https://evoappvn-stag.onelink.me/Blki';

  /// One link on uat environment
  static const String oneLinkUATUrl = 'https://evoappvn-uat.onelink.me/0Fmo';

  /// One link on prod environment
  static const String oneLinkPRODUrl = 'https://evoappvn.onelink.me/QA15';

  static String getOneLinkURLByEnv() {
    final String flavor = FlavorConfig.instance.flavor;

    if (flavor == FlavorType.prod.name) {
      return OneLinkURLConstants.oneLinkPRODUrl;
    } else if (flavor == FlavorType.uat.name) {
      return OneLinkURLConstants.oneLinkUATUrl;
    }

    return OneLinkURLConstants.oneLinkStagingUrl;
  }
}

/// Refer OneLink attribute document
/// https://support.appsflyer.com/hc/en-us/articles/207447163-Attribution-link-structure-and-parameters#attribution-link-parameters%E2%80%94ua-and-retargeting
enum OneLinkKey {
  /// The URI scheme fallback value to launch the app
  afDP('af_dp'),

  /// Campaign
  c('c'),

  /// Must use af_xp to pass user-specific data, campaign identifiers,
  /// or other metadata that your app can read and use when the user opens the app through the deep link.
  afXp('af_xp'),

  /// Uniquely identifies an AppsFlyer integrated partner.
  pid('pid'),

  /// Force deep linking into the activity specified in af_dp value
  afForceDeeplink('af_force_deeplink'),

  /// AppsFlyer - OneLink use the 'deep_link_value' query parameter to store the deep link value
  /// Android: https://dev.appsflyer.com/hc/docs/dl_android_unified_deep_linking#flow
  /// iOS: https://dev.appsflyer.com/hc/docs/dl_ios_unified_deep_linking#flow
  ///
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3433234592/EVO+App+Deep+Links
  deepLinkValue('deep_link_value');

  final String value;

  const OneLinkKey(this.value);
}

/// https://trustingsocial1.atlassian.net/browse/EMA-3010
enum EvoNFCCampaign {
  nfcCampaignStag('evo_dop_nfc_stag'),
  nfcCampaignUAT('evo_dop_nfc_uat'),
  nfcCampaignProd('evo_dop_nfc');

  final String value;

  const EvoNFCCampaign(this.value);

  static EvoNFCCampaign getByEnv() {
    final String flavor = FlavorConfig.instance.flavor;

    return <String, EvoNFCCampaign>{
          FlavorType.prod.name: nfcCampaignProd,
          FlavorType.uat.name: nfcCampaignUAT,
          FlavorType.stag.name: nfcCampaignStag,
        }[flavor] ??
        nfcCampaignStag;
  }
}

class EvoOneLinkCampaign {
  /// https://trustingsocial1.atlassian.net/browse/EMA-3010
  static String get nfcCampaign => EvoNFCCampaign.getByEnv().value;
}
