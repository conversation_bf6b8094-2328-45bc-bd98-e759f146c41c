// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_markdown_plus/flutter_markdown_plus.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../dop_native_constants.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import 'cubit/dop_native_personalize_reward_cubit.dart';
import 'cubit/dop_native_personalize_reward_state.dart';
import 'widgets/dop_native_personalize_reward_list_widget.dart';

class DOPNativePersonalizeRewardScreen extends PageBase {
  final DOPNativePersonalizeRewardCubit? cubit;

  const DOPNativePersonalizeRewardScreen({
    this.cubit,
    super.key,
  });

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativePersonalizeRewardScreen.name,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativePersonalizeRewardScreen> createState() =>
      DOPNativePersonalizeRewardScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativePersonalizeRewardScreen.routeName);
}

class DOPNativePersonalizeRewardScreenState
    extends DOPNativePageStateBase<DOPNativePersonalizeRewardScreen> {
  final double imgHeight = 100;

  late final DOPNativePersonalizeRewardCubit cubit = widget.cubit ??
      DOPNativePersonalizeRewardCubit(
        appState: getIt<AppState>(),
        dopNativeRepo: getIt<DOPNativeRepo>(),
        metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
      );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      cubit.loadRewards();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: BlocProvider<DOPNativePersonalizeRewardCubit>(
          create: (_) => cubit,
          child: BlocConsumer<DOPNativePersonalizeRewardCubit, DOPNativePersonalizeRewardState>(
            listener: (BuildContext context, DOPNativePersonalizeRewardState state) {
              handleStateChanged(state);
            },
            builder: (BuildContext context, DOPNativePersonalizeRewardState state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  buildHeader(state),
                  buildRewardList(state),
                  _buildCTAButton(state),

                  // Bottom padding
                  const SizedBox(height: 20),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget buildHeader(DOPNativePersonalizeRewardState state) {
    DOEPersonalizeRewardHeader? header;

    if (state is DOPNativePersonalizeRewardLoaded) {
      header = state.entity.header;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: <Widget>[
          const SizedBox(height: 40),
          evoImageProvider.asset(
            DOPNativeImages.imgPersonalizeReward,
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: imgHeight / DOPNativeConstants.figmaScreenHeight,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            header?.title ?? '',
            style: dopNativeTextStyles.h500(),
          ),
          const SizedBox(height: 12),
          MarkdownBody(
            data: header?.desc ?? '',
            fitContent: false,
            styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
              p: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
              strong: dopNativeTextStyles.h300(color: dopNativeColors.textPassive),
              textAlign: WrapAlignment.center,
            ),
          ),
          const SizedBox(height: 16),
          buildNoteSection(header),
        ],
      ),
    );
  }

  Widget buildNoteSection(DOEPersonalizeRewardHeader? header) {
    final int waitingTimeInSeconds = header?.waitingTime ?? 0;
    final int waitingTimeInMinute = (waitingTimeInSeconds / 60).round();

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          width: 0.5,
          color: dopNativeColors.border,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Text.rich(
        TextSpan(
          children: <InlineSpan>[
            TextSpan(
                text: '${DOPNativeStrings.dopNativeNote}: ', style: dopNativeTextStyles.h100()),
            TextSpan(
              text: DOPNativeStrings.dopNativePersonalizeRewardTitle.replaceVariableByValue(
                <String>[
                  waitingTimeInMinute.toString(),
                  header?.defaultName ?? '',
                ],
              ),
              style: dopNativeTextStyles.bodySmall(
                color: dopNativeColors.textPassive,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildRewardList(DOPNativePersonalizeRewardState state) {
    final List<DOEPersonalizeRewardCategory> categories = <DOEPersonalizeRewardCategory>[];
    DOEPersonalizeRewardCategory? selectedItem;

    if (state is DOPNativePersonalizeRewardLoaded) {
      categories.addAll(state.entity.category ?? <DOEPersonalizeRewardCategory>[]);
    }

    if (state is DOPNativePersonalizeRewardSelected) {
      selectedItem = state.selectedItem;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 40),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 36),
          child: Text(
            DOPNativeStrings.dopNativePersonalizeRewardChooseCategory,
            style: dopNativeTextStyles.bodySmall(color: dopNativeColors.textDisable),
          ),
        ),
        DOPNativePersonalizeRewardListWidget(
          categories: categories,
          selectedItem: selectedItem,
          onCategorySelected: cubit.onSelectReward,
        ),
      ],
    );
  }

  Widget _buildCTAButton(DOPNativePersonalizeRewardState state) {
    DOEPersonalizeRewardCategory? selectedItem;

    if (state is DOPNativePersonalizeRewardSelected) {
      selectedItem = state.selectedItem;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: CommonButton(
        onPressed: selectedItem == null ? null : cubit.submitReward,
        style: dopNativeButtonStyles.primary(ButtonSize.medium),
        child: const Text(DOPNativeStrings.dopNativeNext),
      ),
    );
  }

  @visibleForTesting
  Widget buildCTAButton() {
    return BlocBuilder<DOPNativePersonalizeRewardCubit, DOPNativePersonalizeRewardState>(
      buildWhen: (_, DOPNativePersonalizeRewardState current) {
        return current is DOPNativePersonalizeRewardSelected;
      },
      builder: (BuildContext context, DOPNativePersonalizeRewardState state) {
        return _buildCTAButton(state);
      },
    );
  }

  @visibleForTesting
  void handleStateChanged(DOPNativePersonalizeRewardState state) {
    if (state is DOPNativePersonalizeRewardLoading) {
      showDOPLoading();
      return;
    }

    if (state is DOPNativePersonalizeRewardSubmitSuccess) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    hideDOPLoading();
    if (state is DOPNativePersonalizeRewardError) {
      handleDopEvoApiError(state.error);
      return;
    }
  }
}
