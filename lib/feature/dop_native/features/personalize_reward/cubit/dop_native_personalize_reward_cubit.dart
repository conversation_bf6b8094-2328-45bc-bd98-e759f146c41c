// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../util/metadata/dop_native_metadata_utils.dart';
import '../mock_file/mock_dop_native_personalize_reward.dart';
import 'dop_native_personalize_reward_state.dart';

class DOPNativePersonalizeRewardCubit extends CommonCubit<DOPNativePersonalizeRewardState> {
  final AppState appState;
  final DOPNativeRepo dopNativeRepo;
  final DOPNativeMetadataUtils metadataUtils;

  @visibleForTesting
  List<DOEPersonalizeRewardCategory> categories = <DOEPersonalizeRewardCategory>[];

  @visibleForTesting
  DOEPersonalizeRewardCategory? selectedItem;

  DOPNativePersonalizeRewardCubit({
    required this.appState,
    required this.dopNativeRepo,
    required this.metadataUtils,
  }) : super(DOPNativePersonalizeRewardInitial());

  void onSelectReward(DOEPersonalizeRewardCategory selectedCategory) {
    selectedItem = selectedCategory;
    emit(DOPNativePersonalizeRewardSelected(selectedCategory));
  }

  Future<void> submitReward() async {
    emit(DOPNativePersonalizeRewardLoading());

    //Todo: implement API call to submit selected reward category
    // final BaseEntity response = await dopNativeRepo.submitPersonalizeReward(
    //   categoryCode: selectedCategory.code,
    //   mockConfig: MockConfig(enable: false),
    // );
    //
    // if (response.statusCode != CommonHttpClient.SUCCESS) {
    //   emit(DOPNativePersonalizeRewardError(ErrorUIModel.fromEntity(response)));
    //   return;
    // }

    emit(DOPNativePersonalizeRewardSubmitSuccess());
  }

  Future<void> loadRewards() async {
    emit(DOPNativePersonalizeRewardLoading());

    final DOEPersonalizeRewardEntity response = await dopNativeRepo.getPersonalizeReward(
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockDOPNativePersonalizeReward(
            MockDOPNativePersonalizeRewardCase.getPersonalizeRewardSuccess),
      ),
    );

    if (response.statusCode != CommonHttpClient.SUCCESS) {
      emit(DOPNativePersonalizeRewardError(ErrorUIModel.fromEntity(response)));
      return;
    }

    emit(DOPNativePersonalizeRewardLoaded(response));
  }
}
