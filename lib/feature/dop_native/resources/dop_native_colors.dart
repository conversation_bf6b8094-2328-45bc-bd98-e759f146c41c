import 'package:flutter/material.dart';

class DOPNativeColors {
  /// start: region Common
  Color get error => const Color(0xFFE54D2E);

  Color get divider => const Color(0xFFE9E9E9);

  Color get border => const Color(0xFFD1D1D1);

  /// end: region Common

  /// start: region background
  Color get primary => const Color(0xFF5E2E86);

  Color get background => const Color(0xFFFFFFFF);

  Color get screenBackground => const Color(0xFFEDEEF2);

  /// end: region background

  /// start: region text
  Color get textActive => const Color(0xFF1D1D1D);

  Color get textPassive => const Color(0xFF5E5E5E);

  Color get textPassive2 => const Color(0xFFFFFFFF);

  Color get textPrimary => const Color(0xFF7A318A);

  Color get textPrimary2 => const Color(0xFF43236C);

  Color get hintText => const Color(0xFF999999);

  Color get icon => const Color(0xFF1D1D1D);

  Color get textDisable => const Color(0xFF808080);

  /// end: region text

  /// start: Shadow
  Color get shadow => const Color(0xFF212F3A).withOpacity(0.2);

  /// end: Shadow

  /// start: region TextField
  Color get focusedTextFieldBorder => const Color(0xFF1D1D1D).withOpacity(0.2);

  Color get textFieldBorder => const Color(0xFF1D1D1D).withOpacity(0.2);

  Color get textFieldBg => background;

  Color get textCursor => const Color(0xFF000000);

  /// end: region TextField

  /// start: region button
  // primary button
  Color get primaryButtonForeground => Colors.white;

  Color get primaryButtonBg => const Color(0xFFF58220);

  Color get primaryButtonForegroundDisable => Colors.white;

  Color get primaryButtonBgDisable => const Color(0xFFC8CBD6);

  // tertiary button
  Color get tertiaryButtonForeground => const Color(0xFF808080);

  Color get tertiaryButtonBg => const Color(0xFFEDEEF2);

  Color get tertiaryButtonForegroundDisable => const Color(0xFFEDEEF2);

  Color get tertiaryButtonBgDisable => const Color(0xFFFFFFFF);

  // icon button
  Color get iconButtonBackground => const Color(0xFFFFFFFF);

  /// end: region button

  /// DOP Native OTP
  Color get otpSelectedBorder => const Color(0xFFD82D8D);

  Color get otpSelectedBackground => const Color(0xFFEDEEF2);

  Color get otpNotInputtedBorder => const Color(0xFF5E2E86).withOpacity(0.3);

  Color get otpNotInputtedBackground => const Color(0xFFEDEEF2);

  Color get otpInputtedBorder => const Color(0xFFE2E3E7);

  Color get otpInputtedBackground => const Color(0xFFE2E3E7);

  Color get otpText => const Color(0xFF5E2E86);

  Color get otpCursor => const Color(0xFF262834);

  Color get otpOtpError => error;

  Color get textGradientStart => const Color(0xFF5E2E86);

  Color get textGradientEnd => const Color(0xFFD82D8D);

  Color get otpCountdown => error;

  /// DOP Introduction Screen
  Color get openCardPrimaryBg => const Color(0xFFFEC009);

  Color get bgFooterGradientStart => const Color(0xFFE47AFF);

  Color get bgFooterGradientEnd => const Color(0xFF43236C);

  Color get lineOfFooterGradientStart => const Color(0xFF4E2871);

  Color get lineOfFooterGradientMiddle => const Color(0xFFC64A86);

  Color get lineOfFooterGradientEnd => const Color(0xFFF58836);

  /// Sub Introduction
  Color get subIntroductionFooterGradientStart => const Color(0xFFE47AFF);

  Color get subIntroductionFooterGradientEnd => const Color(0xFF8D56E8);

  /// DOP ID verification introduction
  Color get dopNativeProgressIndicatorBg => const Color(0xFFE0E4EA);

  Color get semanticInfoColor => const Color(0xFF1F71F4);

  /// DOP checkbox
  Color get dopNativeCheckBoxChecked => const Color(0xFF7A318A);

  Color get dopNativeCheckBoxUnChecked => const Color(0xFFFFFFFF);

  Color get dopNativeCheckBoxBorderUnChecked => const Color(0xFFD1D1D1);

  Color get dopNativeCheckBoxBorderChecked => const Color(0xFF7A318A);

  /// DOP radio box
  Color get dopNativeRadioBoxChecked => const Color(0xFF7A318A);

  Color get dopNativeRadioBoxBorderChecked => const Color(0xFF7A318A);

  Color get dopNativeRadioBoxBorderUnChecked => const Color(0xFF999999);

  Color get dopNativeRadioBoxBackground => const Color(0xFFFFFFFF);

  /// Card activate
  Color get dopNativeCardActivateBackground => const Color(0xFFF3E5F6);

  Color get dopNativeCardActivateBorder => const Color(0x331D1D1D);

  Color get dopNativeCardActivateShadow1 => const Color(0x29000000);

  Color get dopNativeCardActivateShadow2 => const Color(0x14000000);

  /// E-sign
  Color get dopNativeESignProclamationText => const Color(0xFF262834);

  Color get dopNativeESignProclamationHighlightText => const Color(0xFFF15500);

  Color get dopNativeTooltipBackground => const Color(0x99212225);

  Color get dopNativeZoomInEContractGuide => const Color(0xE5808A99);

  Color get dopNativeAmericaCitizenTitle => const Color(0xFF434549);

  Color get dopNativeESignRoundedBoxText => const Color(0xFF434549);

  /// Pdf Review
  Color get pdfReviewShadow => const Color(0x28002A49);

  /// POS Limit
  Color get posLimitNoteBorder => const Color(0xFF1D1D1D).withOpacity(0.08);

  /// Address Additional Info
  Color get companyNameTextFieldBorder => const Color(0xFFD82D8D);

  /// Acquisition Reward
  Color get acquisitionRewardGradientStart => const Color(0xFF8D56E8);

  Color get acquisitionRewardGradientEnd => const Color(0xFFE47AFF);

  /// DOP NFC Device unsupported screen
  Color get dopNativeDOPDeviceUnsupportedTextSecondary => const Color(0xFF434549);

  Color get dopNativeNoteContainerItemTextColor => const Color(0xFF2A2C32);
}
