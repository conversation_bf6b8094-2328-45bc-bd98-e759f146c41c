import 'package:flutter/material.dart';

class DOPNativeImages {
  @visibleForTesting
  static const String png = '.png';
  @visibleForTesting
  static const String svg = '.svg';

  @visibleForTesting
  static const String assetPath = 'assets/dop_native_images/';

  /// DOP native PNG
  static const String icTpBankLogo = '${assetPath}ic_tp_bank_logo$png';

  // Introduction screen
  static const String imgHeaderTPBank = '${assetPath}img_header_tp_bank$png';
  static const String bgDOPNativeIntroduction = '${assetPath}bg_dop_native_introduction$png';
  static const String imgTitleTopOfIntroduction = '${assetPath}img_title_top_of_introduction$png';
  static const String imgCashBack10PercentBanner =
      '${assetPath}img_cash_back_10_percent_banner$png';
  static const String imgCashBackNoLimitBanner = '${assetPath}img_cash_back_no_limit_banner$png';
  static const String imgNoUseInformationBanner = '${assetPath}img_no_use_information_banner$png';
  static const String imgUseEVOCardBanner = '${assetPath}img_use_evo_card_banner$png';
  static const String imgEMI0PercentBanner = '${assetPath}img_emi_0_percent_banner$png';
  static const String imgFreeAnnualFeeBanner = '${assetPath}img_free_annual_fee_banner$png';
  static const String imgFreeUpToBanner = '${assetPath}img_free_up_to_banner$png';
  static const String imgTitleIntroductionFooter = '${assetPath}img_title_introduction_footer$png';
  static const String imgCardIntroductionFooter = '${assetPath}img_card_introduction_footer$png';
  static const String imgFooterLine = '${assetPath}img_footer_line$png';
  static const String imgMadeByFooter = '${assetPath}img_made_by_footer$png';
  static const String imgIntroIconFooter = '${assetPath}img_intro_icon_footer$png';
  static const String imgSupportIconFooter = '${assetPath}img_support_icon_footer$png';
  static const String imgQuestionIconFooter = '${assetPath}img_question_icon_footer$png';
  static const String icFaceOTPInvalid = '${assetPath}ic_face_otp_invalid$png';
  static const String imgSelfieVerifySuccess = '${assetPath}img_selfie_verify_success$png';
  static const String imgEvoCardSmall = '${assetPath}img_evo_cards_small$png';
  static const String imgEvoSettingSmall = '${assetPath}img_evo_setting_small$png';

  // DOP Native Appraising Verification Screen
  static const String imgAppraisingVerification = '${assetPath}img_appraising_verification$png';

  // DOP Native failure screen
  static const String icDOPRejectUserRegister = '${assetPath}ic_dop_reject_user_register$png';

  /// DOP native SVG
  static const String icOtpCodeError = '${assetPath}ic_otp_code_error$svg';
  static const String icOtpRateLimit = '${assetPath}ic_otp_rate_limit$svg';
  static const String icEvoLogo = '${assetPath}ic_evo_logo$svg';
  static const String icHelpChat = '${assetPath}ic_help_chat$svg';
  static const String icArrowRight = '${assetPath}ic_arrow_right$svg';
  static const String icArrowLeft = '${assetPath}ic_arrow_left$svg';
  static const String icArrowRightDisabled = '${assetPath}ic_arrow_right_disabled$svg';
  static const String icErrorTextField = '${assetPath}ic_error_text_field$svg';
  static const String icSMSPhone = '${assetPath}ic_sms_phone$svg';
  static const String welcomeBackImage = '${assetPath}img_welcome_back$svg';
  static const String icScrollToTopIntroduction = '${assetPath}ic_scroll_to_top_introduction$svg';
  static const String icClose = '${assetPath}ic_close$svg';
  static const String icCloseFlat = '${assetPath}ic_close_flat$svg';
  static const String icBack = '${assetPath}ic_back$svg';
  static const String icArrowDown = '${assetPath}ic_arrow_down$svg';
  static const String icSuffixTextFieldError = '${assetPath}ic_suffix_text_field_error$svg';
  static const String icSuffixTextFieldCheck = '${assetPath}ic_suffix_text_field_check$svg';
  static const String icInfoOutline = '${assetPath}ic_info_outline$svg';
  static const String icTimeout = '${assetPath}ic_timeout$svg';

  /// DOP Native ID verification introduction
  static const String imgIdVerification = '${assetPath}img_id_verification$svg';
  static const String imgIdVerificationAware1 = '${assetPath}img_id_verification_aware_1$svg';
  static const String imgIdVerificationAware2 = '${assetPath}img_id_verification_aware_2$svg';
  static const String imgIdVerificationAware3 = '${assetPath}img_id_verification_aware_3$svg';
  static const String icDOPCamera = '${assetPath}ic_dop_camera$svg';

  /// DOP Native ID verification confirm
  static const String icDOPNativeEdit = '${assetPath}ic_dop_native_edit$svg';

  /// DOP Native eKYC processing
  static const String imgEkycProcessing = '${assetPath}img_ekyc_processing$svg';

  /// DOP Native eKYC success
  static const String imgIDCard = '${assetPath}img_id_card$svg';

  /// DOP Native eKYC error
  static const String imgInvalidID = '${assetPath}img_invalid_id$svg';
  static const String imgInvalidCaptured = '${assetPath}img_invalid_captured$svg';
  static const String imgExceedCapturing = '${assetPath}img_exceed_capturing$svg';

  /// DOP Native Selfie verification instruction
  static const String imgSelfieCapture = '${assetPath}img_selfie_capture$svg';
  static const String imgSelfieCaptureAware1 = '${assetPath}img_selfie_capture_aware_1$png';
  static const String imgSelfieCaptureAware2 = '${assetPath}img_selfie_capture_aware_2$png';
  static const String imgSelfieCaptureAware3 = '${assetPath}img_selfie_capture_aware_3$png';
  static const String imgSelfieVerification = '${assetPath}img_selfie_verification$svg';

  /// DOP Native internet error
  static const String icInternetError = '${assetPath}ic_internet_error$svg';

  /// DOP Native sand lock icon
  static const String icSandClock = '${assetPath}ic_sand_clock$svg';

  /// Inform success
  static const String imgEvoCards = '${assetPath}img_evo_cards$png';

  /// DOP Native App Form Additional Info
  static const String icBackStep = '${assetPath}ic_back_step$svg';

  ///DOP native checkbox
  static const String icCheck = '${assetPath}ic_check$svg';
  static const String icDopNativeInfo = '${assetPath}ic_dop_native_info$svg';

  // EContract, TPB contact & TPB warning widget
  static const String icRejectWithdrawMoneyInvitation =
      '${assetPath}ic_reject_withdraw_money_invitation$svg';
  static const String icProtectCVV = '${assetPath}ic_protect_cvv$svg';

  /// Card activation status
  static const String icCardVerify = '${assetPath}ic_card_verify$svg';

  /// E-Sign review
  static const String icInfo = '${assetPath}ic_info$svg';
  static const String icZoomIn = '${assetPath}ic_zoom_in$svg';
  static const String icClock = '${assetPath}ic_clock$svg';

  /// NFC reader instruction
  static const String imgNFCReader = '${assetPath}img_nfc_reader$svg';
  static const String imgNFCAware1 = '${assetPath}img_nfc_aware_1$svg';
  static const String imgNFCAware2 = '${assetPath}img_nfc_aware_2$svg';
  static const String imgNFCAware3 = '${assetPath}img_nfc_aware_3$svg';
  static const String icPhone = '${assetPath}ic_phone$svg';

  static const String imgNFCGuide1 = '${assetPath}img_nfc_guide_1$svg';
  static const String imgNFCGuide2 = '${assetPath}img_nfc_guide_2$svg';

  /// DOP Acquisition Reward
  static const String imgAcquisitionReward = '${assetPath}img_acquisition_reward$svg';
  static const String imgAcquisitionRewardItem = '${assetPath}img_acquisition_reward_item$svg';
  static const String imgAcquisitionRewardItemDashSeparator =
      '${assetPath}img_acquisition_reward_item_dash_separator$svg';

  /// DOP FaceOTP
  static const String imgFaceOTPSuccess = '${assetPath}img_face_otp_success$png';

  /// Video Player
  static const String icPlay = '${assetPath}ic_play$svg';
  static const String icReload = '${assetPath}ic_reload$svg';

  /// Switch flow
  static const String imgExistingRecord = '${assetPath}img_existing_record$svg';
  static const String imgDuplicateRecord = '${assetPath}img_duplicate_record$svg';

  /// Collect Location
  static const String imgCollectLocation = '${assetPath}img_collect_location$svg';

  /// Sophia call
  static const String imgSophiaCall = '${assetPath}img_sophia_call$svg';
  static const String icClockOutline = '${assetPath}ic_clock_outline$svg';

  /// Personalize reward
  static const String imgPersonalizeReward = '${assetPath}img_personalize_reward$svg';
}
