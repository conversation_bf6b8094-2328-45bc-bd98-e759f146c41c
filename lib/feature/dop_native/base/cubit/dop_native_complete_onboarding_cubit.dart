import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/sign_in_otp_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/evo_authentication_helper.dart';
import '../../../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import '../../models/dop_native_state.dart';
import '../../util/dop_functions.dart';
import '../../util/mock_file/mock_login_from_dop_native_use_case.dart';

part 'dop_native_complete_onboarding_state.dart';

class DOPNativeCompleteOnboardingCubit extends CommonCubit<DOPNativeCompleteOnboardingState> {
  final AuthenticationRepo authenticationRepo;
  final AppState appState;

  DOPNativeCompleteOnboardingCubit({
    required this.authenticationRepo,
    required this.appState,
  }) : super(DOPNativeCompleteOnboardingInitial());

  @visibleForTesting
  Future<void> logoutEvoIfNeeded() async {
    if (!appState.isUserLogIn) {
      EvoAuthenticationHelper().clearAllUserData();
      return;
    }

    /// Should await logout API even don't handle the response
    /// Because this API use access_token in the request header
    /// If we don't await this API, the access_token can be removed before the request is sent
    await authenticationRepo.logout(
        mockConfig: MockConfig(enable: false, fileName: getSignOutMockFileName()));

    EvoAuthenticationHelper().clearAllUserData();
  }

  Future<void> onExitDOPFlow() async {
    final DOPNativeState dopAppState = appState.dopNativeState;

    await signInToEvo(
      dopAccessToken: dopAppState.dopNativeAccessToken,
      dopPhoneNumber: dopAppState.phoneNumber,
    );

    dopUtilFunction.clearDOPNativeData();
  }

  @visibleForTesting
  Future<void> signInToEvo({String? dopAccessToken, String? dopPhoneNumber}) async {
    emit(DOPNativeCompleteOnboardingLoading());

    final SignInOtpEntity signInEntity = await authenticationRepo.loginFromDOE(
      type: TypeLogin.none,
      dopAccessToken: dopAccessToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getLoginFromDOPNativeFileName(MockLoginFromDOPNativeUseCase.loginFail),
      ),
    );

    if (signInEntity.statusCode == CommonHttpClient.SUCCESS) {
      if (signInEntity.verdict == SignInOtpEntity.verdictAlreadySignedIn) {
        emit(DOPNativeAlreadySignInEvo());
        return;
      }

      if (dopPhoneNumber == null || dopPhoneNumber.isEmpty) {
        await logoutEvoIfNeeded();
        emit(DOPNativeSignInToEvoFailed());
        return;
      }

      emit(DOPNativeSignInToEVOSuccess(entity: signInEntity, phone: dopPhoneNumber));
    } else if (signInEntity.statusCode == CommonHttpClient.BAD_REQUEST &&
        signInEntity.verdict == SignInOtpEntity.verdictCompleteWithOtherPhone) {
      await logoutEvoIfNeeded();
      emit(DOPNativeCompleteWithAnotherPhone());
    } else {
      await logoutEvoIfNeeded();
      emit(DOPNativeSignInToEvoFailed());
    }
  }
}
