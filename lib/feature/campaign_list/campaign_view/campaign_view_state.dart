part of 'campaign_view_cubit.dart';

abstract class CampaignViewState extends BlocState {}

class CampaignViewLoading extends CampaignViewState {}

class CampaignViewInfoSuccess extends CampaignViewState {
  final CampaignListEntity? campaignListEntity;

  CampaignViewInfoSuccess({this.campaignListEntity});
}

class CampaignViewFail extends CampaignViewState {
  final ErrorUIModel? error;

  CampaignViewFail({this.error});
}
