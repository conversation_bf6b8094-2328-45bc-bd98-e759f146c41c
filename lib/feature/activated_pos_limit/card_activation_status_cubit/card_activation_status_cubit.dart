import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/user_repo.dart';
import '../../../data/response/card_activation_status_entity.dart';
import '../mock/mock_get_card_activation_status_case.dart';

part 'card_activation_status_state.dart';

class CardActivationStatusCubit extends CommonCubit<CardActivationStatusState> {
  final UserRepo userRepo;

  CardActivationStatusCubit({required this.userRepo}) : super(CardActivationStatusInitialState());

  Future<void> getCardActivationStatus() async {
    emit(CardActivationStatusLoadingState());

    final CardActivationStatusEntity entity = await userRepo.getCardActivationStatus(
      mockConfig: MockConfig(
        enable: false,
        fileName:
            getMockCardActivationStatusCaseFileName(MockGetCardActivationStatusCase.activated),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(GetCardActivationStatusSucceedState(entity: entity));
      return;
    }

    emit(GetCardActivationStatusFailedState(errorUIModel: ErrorUIModel.fromEntity(entity)));
  }
}
