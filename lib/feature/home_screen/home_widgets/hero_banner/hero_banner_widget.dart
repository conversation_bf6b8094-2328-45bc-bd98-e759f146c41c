import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/repository/home_repo.dart';
import '../../../../data/response/hero_banner_entity.dart';
import '../../../../model/evo_action_model.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/evo_action_handler.dart';
import '../../../../util/mapper.dart';
import '../../../../widget/evo_inkwell_network_image.dart';
import 'hero_banner_cubit.dart';

///https://trustingsocial1.atlassian.net/browse/EMA-266
class HeroBannerWidget extends UIComponentWidget {
  final void Function(bool)? updateStatusHeroBanner;
  final double? heightBanner;

  const HeroBannerWidget(
      {super.key, super.controller, this.updateStatusHeroBanner, this.heightBanner});

  @override
  UIComponentWidgetState<HeroBannerWidget> createState() => _HeroBannerWidgetState();
}

class _HeroBannerWidgetState extends UIComponentWidgetState<HeroBannerWidget> {
  final HeroBannerCubit _cubit = HeroBannerCubit(homeRepo: getIt.get<HomeRepo>());

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HeroBannerCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<HeroBannerCubit, UiComponentState>(
        listener: (_, UiComponentState state) {
          _handleListener(state);
        },
        builder: (_, UiComponentState state) {
          return _itemImage(state);
        },
      ),
    );
  }

  Widget _itemImage(UiComponentState state) {
    const double toolbarHeight = 64;
    const double heightHomeBottomAppBar = 19;
    bool hasHeroBanner = false;
    HeroBannerEntity? heroBannerEntity;
    if (state is UiComponentDataLoaded<HeroBannerEntity>) {
      heroBannerEntity = state.data;
      hasHeroBanner = heroBannerEntity?.imageUrl?.isNotEmpty == true;
    }
    return hasHeroBanner
        ? EvoInkWellNetworkImage(
            heroBannerEntity?.imageUrl,
            height: widget.heightBanner,
            width: double.infinity,
            placeholder: const SizedBox.shrink(),
            errorWidget: _itemErrorImage(),
            fit: BoxFit.cover,
            onTap: () {
              onTapHeroBanner(heroBannerEntity);
            },
          )
        : Container(
            color: evoColors.primary,
            height: toolbarHeight + context.screenPadding.top + heightHomeBottomAppBar,
            width: context.screenWidth,
          );
  }

  Widget _itemErrorImage() => evoImageProvider.asset(
        EvoImages.icPreBannerImage,
        fit: BoxFit.cover,
        height: widget.heightBanner,
        width: context.screenWidth,
      );

  @override
  void loadData() {
    _cubit.getHeroBanner();
  }

  void _handleListener(UiComponentState state) {
    handleCommonEvent(state);
    bool hasHeroBanner = false;
    if (state is UiComponentDataLoaded<HeroBannerEntity>) {
      hasHeroBanner = state.data?.imageUrl?.isNotEmpty == true;
    }
    widget.updateStatusHeroBanner?.call(hasHeroBanner);
  }

  void onTapHeroBanner(HeroBannerEntity? heroBannerEntity) {
    heroBannerEntity?.action?.toEvoActionModel().let((EvoActionModel action) async {
      EvoActionHandler().handle(action);
    });
  }
}
