import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../utils/biometrics_authenticate.dart';
import 'active_biometric_state.dart';

class ActiveBiometricCubit extends CommonCubit<ActiveBiometricState> {
  final BiometricsAuthenticate bioAuth;
  final EvoLocalStorageHelper localStorage;

  ActiveBiometricCubit({
    required this.bioAuth,
    required this.localStorage,
  }) : super(ActiveBiometricInitState());

  Future<void> checkEnrolledBiometric() async {
    final bool hasEnrolledBiometrics = await bioAuth.hasEnrolledBiometric();
    emit(EnrolledBiometricState(hasEnrolledBiometrics: hasEnrolledBiometrics));
  }

  Future<void> setNewDevice(bool isNewDevice) async {
    await localStorage.setNewDevice(isNewDevice);
  }
}
