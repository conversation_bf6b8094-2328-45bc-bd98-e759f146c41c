import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOPNativeBootstrapEntity extends BaseEntity {
  final String? lenderCode;
  final String? uiVersion;
  final String? maskedPhoneNumber;
  final List<String?>? requiredSteps;
  final int? flowSelectedAt;
  final String? leadSource;

  DOPNativeBootstrapEntity({
    this.lenderCode,
    this.uiVersion,
    this.maskedPhoneNumber,
    this.requiredSteps,
    this.flowSelectedAt,
    this.leadSource,
  });

  DOPNativeBootstrapEntity.unserializable()
      : lenderCode = null,
        uiVersion = null,
        maskedPhoneNumber = null,
        requiredSteps = null,
        flowSelectedAt = null,
        leadSource = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeBootstrapEntity.fromBaseResponse(BaseResponse super.response)
      : lenderCode = response.data?['lender_code'],
        uiVersion = response.data?['ui_version'],
        maskedPhoneNumber = response.data?['masked_phone_number'],
        requiredSteps = (response.data?['required_steps'] as List<dynamic>?)
            ?.map((dynamic e) => e as String?)
            .toList(),
        flowSelectedAt = response.data?['flow_selected_at'],
        leadSource = response.data?['lead_source'],
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'lender_code': lenderCode,
      'ui_version': uiVersion,
      'masked_phone_number': maskedPhoneNumber,
      'required_steps': requiredSteps?.map((String? v) => v).toList(),
      'flow_selected_at': flowSelectedAt,
      'lead_source': leadSource,
    });
    return json;
  }
}
