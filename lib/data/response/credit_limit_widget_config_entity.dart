enum CreditStatus {
  waitingForApproval('waiting_for_approval'),
  notReadyForPayment('not_ready_for_payment'),
  readyForPayment('ready_for_payment'),

  /// The status is returned when the DOP State as successful more than 90 days.
  /// After 90 Days, BE will not call to DOP again to get the latest status.
  outOfSyncWithDOP('out_of_sync');

  final String value;

  const CreditStatus(this.value);
}

class CreditLimitWidgetConfigEntity {
  final bool? display;
  final int? creditLimit;
  final String? creditStatus;

  CreditLimitWidgetConfigEntity({
    this.display,
    this.creditLimit,
    this.creditStatus,
  });

  CreditLimitWidgetConfigEntity.fromJson(Map<String, dynamic> json)
      : display = json['display'] as bool?,
        creditLimit = json['credit_limit'] as int?,
        creditStatus = json['status'] as String?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'display': display,
        'credit_limit': creditLimit,
        'status': creditStatus,
      };
}

extension CreditLimitWidgetConfigEntityExt on CreditLimitWidgetConfigEntity {
  bool get isCardApprovedOrOutOfSync =>
      isCardReadyForPayment || isCardNotReadyForPayment || isOutOfSyncWithDOP;

  bool get isCardReadyForPayment => creditStatus == CreditStatus.readyForPayment.value;

  bool get isOutOfSyncWithDOP => creditStatus == CreditStatus.outOfSyncWithDOP.value;

  bool get isCardNotReadyForPayment => creditStatus == CreditStatus.notReadyForPayment.value;
}
