import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'card_benefits_button_item_entity.dart';
import 'card_benefits_item_entity.dart';

class CardBenefitsEntity extends BaseEntity {
  final List<CardBenefitsItemEntity>? cardImages;
  final List<CardBenefitsButtonItemEntity>? buttons;

  CardBenefitsEntity({this.cardImages, this.buttons});

  CardBenefitsEntity.unserializable()
      : cardImages = null,
        buttons = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  CardBenefitsEntity.fromBaseResponse(BaseResponse super.response)
      : cardImages = (response.data?['card_images'] as List<dynamic>?)
            ?.map((dynamic e) => CardBenefitsItemEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        buttons = (response.data?['buttons'] as List<dynamic>?)
            ?.map((dynamic e) => CardBenefitsButtonItemEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'card_images': cardImages?.map((CardBenefitsItemEntity v) => v.toJson()).toList(),
      'buttons': buttons?.map((CardBenefitsButtonItemEntity v) => v.toJson()).toList(),
    });
    return json;
  }
}
